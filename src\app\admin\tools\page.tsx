'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Download,
  Upload,
  CheckCircle,
  Clock,
  Archive
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ImportExportPanel } from '@/components/admin/tools/ImportExportPanel';

interface Tool {
  id: string;
  name: string;
  slug: string;
  description: string;
  website_url: string;
  category_id: string;
  subcategory_id?: string;
  content_status: 'draft' | 'published' | 'archived';
  created_at: string;
  updated_at: string;
  featured: boolean;
  pricing_type: string;
}

export default function ToolsManagementPage() {
  const router = useRouter();
  const [tools, setTools] = useState<Tool[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedTools, setSelectedTools] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [showImportExport, setShowImportExport] = useState(false);

  // Load tools data
  useEffect(() => {
    const loadTools = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/admin/tools', {
          headers: {
            'x-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || ''
          }
        });

        if (!response.ok) {
          throw new Error('Failed to load tools');
        }

        const data = await response.json();
        setTools(data.tools || []);
      } catch (err) {
        setError('Failed to load tools');
        console.error('Error loading tools:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadTools();
  }, []);

  // Filter tools
  const filteredTools = tools.filter(tool => {
    const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || tool.content_status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const handleSelectTool = (toolId: string) => {
    setSelectedTools(prev => 
      prev.includes(toolId) 
        ? prev.filter(id => id !== toolId)
        : [...prev, toolId]
    );
  };

  const handleSelectAll = () => {
    if (selectedTools.length === filteredTools.length) {
      setSelectedTools([]);
    } else {
      setSelectedTools(filteredTools.map(tool => tool.id));
    }
  };

  const handleBulkStatusUpdate = async (status: string) => {
    if (selectedTools.length === 0) return;

    try {
      const response = await fetch('/api/admin/tools/bulk', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || ''
        },
        body: JSON.stringify({
          toolIds: selectedTools,
          updates: { content_status: status }
        })
      });

      if (response.ok) {
        // Refresh tools list
        window.location.reload();
      }
    } catch (err) {
      console.error('Bulk update failed:', err);
    }
  };

  const handleDeleteTool = async (toolId: string) => {
    if (!confirm('Are you sure you want to delete this tool?')) return;

    try {
      const response = await fetch(`/api/admin/tools/${toolId}`, {
        method: 'DELETE',
        headers: {
          'x-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || ''
        }
      });

      if (response.ok) {
        setTools(prev => prev.filter(tool => tool.id !== toolId));
      }
    } catch (err) {
      console.error('Delete failed:', err);
    }
  };

  const handleImportComplete = () => {
    // Reload tools after import
    window.location.reload();
  };

  const handleError = (error: string) => {
    setError(error);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'draft':
        return <Clock className="w-4 h-4 text-yellow-400" />;
      case 'archived':
        return <Archive className="w-4 h-4 text-gray-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'text-green-400';
      case 'draft':
        return 'text-yellow-400';
      case 'archived':
        return 'text-gray-400';
      default:
        return 'text-gray-400';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-gray-400 mt-4">Loading tools...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900 border border-red-700 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-red-200">Error</h3>
        <p className="text-red-300 mt-2">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 bg-red-800 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Tool Management</h1>
          <p className="text-gray-400">Manage AI tools in your directory</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={() => router.push('/admin/tools/new')}
            className="flex items-center space-x-2 bg-blue-700 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>Add Tool</span>
          </button>
          
          <button
            onClick={() => setShowImportExport(!showImportExport)}
            className="flex items-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            {showImportExport ? <Upload className="w-4 h-4" /> : <Download className="w-4 h-4" />}
            <span>{showImportExport ? 'Hide Import/Export' : 'Import/Export'}</span>
          </button>
        </div>
      </div>

      {/* Import/Export Panel */}
      {showImportExport && (
        <ImportExportPanel
          onImportComplete={handleImportComplete}
          onError={handleError}
        />
      )}

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Tools</p>
              <p className="text-xl font-bold text-white">{tools.length}</p>
            </div>
            <Eye className="w-6 h-6 text-blue-400" />
          </div>
        </div>
        
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Published</p>
              <p className="text-xl font-bold text-white">
                {tools.filter(t => t.content_status === 'published').length}
              </p>
            </div>
            <CheckCircle className="w-6 h-6 text-green-400" />
          </div>
        </div>
        
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Drafts</p>
              <p className="text-xl font-bold text-white">
                {tools.filter(t => t.content_status === 'draft').length}
              </p>
            </div>
            <Clock className="w-6 h-6 text-yellow-400" />
          </div>
        </div>
        
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Archived</p>
              <p className="text-xl font-bold text-white">
                {tools.filter(t => t.content_status === 'archived').length}
              </p>
            </div>
            <Archive className="w-6 h-6 text-gray-400" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-zinc-800 border border-black rounded-lg p-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search tools..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-zinc-700 border border-zinc-600 rounded-lg pl-10 pr-4 py-2 text-white focus:outline-none focus:border-blue-500"
              />
            </div>
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
          >
            <option value="all">All Status</option>
            <option value="published">Published</option>
            <option value="draft">Draft</option>
            <option value="archived">Archived</option>
          </select>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedTools.length > 0 && (
        <div className="bg-blue-900 border border-blue-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-blue-200">
              {selectedTools.length} tool{selectedTools.length !== 1 ? 's' : ''} selected
            </span>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleBulkStatusUpdate('published')}
                className="bg-green-700 hover:bg-green-600 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                Publish
              </button>
              <button
                onClick={() => handleBulkStatusUpdate('draft')}
                className="bg-yellow-700 hover:bg-yellow-600 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                Draft
              </button>
              <button
                onClick={() => handleBulkStatusUpdate('archived')}
                className="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                Archive
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Tools Table */}
      <div className="bg-zinc-800 border border-black rounded-lg overflow-hidden">
        <div className="p-4 border-b border-zinc-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-white">Tools ({filteredTools.length})</h2>
            <button
              onClick={handleSelectAll}
              className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
            >
              {selectedTools.length === filteredTools.length ? 'Deselect All' : 'Select All'}
            </button>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          {filteredTools.length === 0 ? (
            <div className="text-center py-8">
              <Eye className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400">No tools found</p>
            </div>
          ) : (
            <table className="w-full">
              <thead className="bg-zinc-700">
                <tr>
                  <th className="text-left p-4">
                    <input
                      type="checkbox"
                      checked={selectedTools.length === filteredTools.length && filteredTools.length > 0}
                      onChange={handleSelectAll}
                      className="rounded"
                    />
                  </th>
                  <th className="text-left p-4 text-gray-300">Name</th>
                  <th className="text-left p-4 text-gray-300">Status</th>
                  <th className="text-left p-4 text-gray-300">Category</th>
                  <th className="text-left p-4 text-gray-300">Created</th>
                  <th className="text-left p-4 text-gray-300">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredTools.map((tool) => (
                  <tr key={tool.id} className="border-t border-zinc-700 hover:bg-zinc-700">
                    <td className="p-4">
                      <input
                        type="checkbox"
                        checked={selectedTools.includes(tool.id)}
                        onChange={() => handleSelectTool(tool.id)}
                        className="rounded"
                      />
                    </td>
                    <td className="p-4">
                      <div>
                        <h3 className="font-medium text-white">{tool.name}</h3>
                        <p className="text-sm text-gray-400 truncate max-w-xs">{tool.description}</p>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(tool.content_status)}
                        <span className={`text-sm font-medium capitalize ${getStatusColor(tool.content_status)}`}>
                          {tool.content_status}
                        </span>
                      </div>
                    </td>
                    <td className="p-4">
                      <span className="text-sm text-gray-400">{tool.category_id}</span>
                    </td>
                    <td className="p-4">
                      <span className="text-sm text-gray-400">
                        {new Date(tool.created_at).toLocaleDateString()}
                      </span>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => router.push(`/tools/${tool.slug}`)}
                          className="text-blue-400 hover:text-blue-300 transition-colors"
                          title="View"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => router.push(`/admin/tools/${tool.id}/edit`)}
                          className="text-yellow-400 hover:text-yellow-300 transition-colors"
                          title="Edit"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteTool(tool.id)}
                          className="text-red-400 hover:text-red-300 transition-colors"
                          title="Delete"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
}
